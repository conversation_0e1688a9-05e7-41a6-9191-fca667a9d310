'use client';

import { Form, FormikProps } from 'formik';

import Button from '@/clients/ui/button';
import FinancialForm, { FinancialFormValues } from './FinancialForm';
import ClausesForm, { ClausesFormValues } from './Clauses';
import PaymentSchedule, { PaymentScheduleValues } from './PaymentSchedule';
import FormBlock from './FormBlock';
import { Lease } from '@/types/lease';
import { useCallback, useEffect } from 'react';
import LeaseInfoForm, { LeaseInfoFormValues } from './LeaseInfoForm';
import { Nullable, ProgressStatus } from '@/types/common';
import { Rent } from '@/types/property';
import { calculateRentForListing } from '@/app/actions/property';
import toast from 'react-hot-toast';
import { format, isValid } from 'date-fns';
import {
  calculateNumberOfDefaultPayments,
  calculateOccupancyTax,
  generatePaymentsForLease,
} from '@/utils/lease';
import { useLease } from '@/clients/contexts/LeaseContext';
import { ensureNumberHas2Decimals, parseStringPrice } from '@/utils/common';
import { UserProfile } from '@/types/profile';
import date from '@/clients/ui/date';

export type LeaseDetailsFormValues = {
  leaseInfo: LeaseInfoFormValues;
  financialInfo: FinancialFormValues;
  clauses: ClausesFormValues;
  paymentSchedule: PaymentScheduleValues;
};

type Props = {
  userData: UserProfile;
  leaseId?: number;
  lease?: Lease;
  progressStatus?: Nullable<ProgressStatus>;
} & FormikProps<LeaseDetailsFormValues>;

const LeaseDetailsFormWrapper = ({
  leaseId,
  lease,
  values,
  errors,
  setFieldValue,
  touched,
  validateField,
  isSubmitting,
  progressStatus,
  validateForm,
  userData,
}: Props) => {
  const {
    setRentInfo,
    listingDetails,
    rentInfo,
    setTenant,
    listingId,
    setListingId,
  } = useLease();

  console.log({ errors, values });

  const calculateRent = useCallback(
    async (arrival_date: string, departure_date: string) => {
      if (listingId) {
        try {
          const rent = await calculateRentForListing<Rent>({
            listing: listingId,
            arrival_date,
            departure_date,
          });
          if (rent) {
            setRentInfo(rent);
          }
        } catch (error) {
          console.log('Failed to get rent', error);
          toast.error('Dates not available');
        }
      }
    },
    [listingId, setRentInfo]
  );

  const getBlockStatus = (touched: any, error: any) => {
    if (!touched) {
      return 'none';
    }

    return error ? 'error' : 'success';
  };

  useEffect(() => {
    const { from, to } = values?.leaseInfo?.dates || {};
    const areDatesValid = from && to && isValid(from) && isValid(to);
    const isEditableLease = !lease || lease?.status === 'Draft';

    if (areDatesValid && isEditableLease) {
      calculateRent(format(from, 'yyyy-MM-dd'), format(to, 'yyyy-MM-dd'));
    }
  }, [
    lease,
    calculateRent,
    values?.leaseInfo?.dates,
    values?.leaseInfo?.dates?.from,
    values?.leaseInfo?.dates?.to,
  ]);

  useEffect(() => {
    if (listingDetails?.requirement) {
      setFieldValue(
        'financialInfo.commission',
        ensureNumberHas2Decimals(
          (listingDetails?.requirement?.commission_percentage ?? 0.15) * 100
        )
      );
    }
  }, [listingDetails?.requirement, setFieldValue]);

  useEffect(() => {
    if (listingDetails?.requirement && rentInfo) {
      setFieldValue(
        'financialInfo.securityDeposit',
        ensureNumberHas2Decimals(
          (listingDetails?.requirement?.min_security_deposit ?? 0.1) *
            (rentInfo?.rent ?? 0)
        )
      );
      setFieldValue('financialInfo.rent', rentInfo?.rent ?? 0);
      setFieldValue(
        'financialInfo.occupancyTax.amount',
        ensureNumberHas2Decimals(
          calculateOccupancyTax(
            rentInfo?.rent ?? 0,
            Number(values?.financialInfo?.processingFee),
            values.financialInfo.otherFees as any,
            false,
            listingDetails?.requirement?.charge_community_impact_fee
          )
        )
      );
    }
  }, [
    listingDetails?.requirement,
    rentInfo,
    setFieldValue,
    values.financialInfo.otherFees,
    values.financialInfo?.processingFee,
  ]);

  useEffect(() => {
    if (rentInfo && values.paymentSchedule && values.leaseInfo.dates) {
      const payments = generatePaymentsForLease(
        values.paymentSchedule.payments.length,
        values.leaseInfo.dates,
        rentInfo?.rent ?? 0,
        values.financialInfo,
        true
      );

      setFieldValue('paymentSchedule.payments', payments);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rentInfo, values.financialInfo]);

  useEffect(() => {
    if (isSubmitting && Object.keys(errors).length > 0) {
      // Scroll to top of form when there are validation errors on submit
      window.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    }
  }, [isSubmitting, errors]);

  useEffect(() => {
    if (lease) {
      validateForm();
    }
  }, [lease, validateForm]);

  return (
    <Form className="w-full flex flex-col md:pb-8">
      <LeaseInfoForm
        listingId={listingId}
        values={values.leaseInfo}
        errors={errors?.leaseInfo}
        setListingId={setListingId}
        setFieldValue={setFieldValue}
        touched={touched?.leaseInfo}
        setTenant={setTenant}
        validateField={validateField}
        lease={lease}
        listingDetails={listingDetails}
        userData={userData}
      />
      <div className="flex-col gap-y-8 md:flex-row">
        <div className="p-4 flex flex-col gap-y-4 md:flex-row gap-x-4 md:px-0">
          <FormBlock
            title="Financial Info"
            status={getBlockStatus(
              touched?.financialInfo,
              errors.financialInfo
            )}
          >
            <FinancialForm
              values={values.financialInfo}
              errors={errors.financialInfo}
              setFieldValue={setFieldValue}
              touched={touched?.financialInfo}
              validateField={validateField}
              lease={lease}
            />
          </FormBlock>
          <FormBlock
            title="Clauses and Additional Language"
            status={getBlockStatus(touched?.clauses, errors.clauses)}
          >
            <ClausesForm
              values={values.clauses}
              errors={errors.clauses}
              setFieldValue={setFieldValue}
              validateField={validateField}
              lease={lease}
            />
          </FormBlock>
        </div>
        <div className="p-4 flex flex-col gap-y-4 md:flex-row gap-x-4 md:px-0 mt-4">
          <FormBlock
            title="Payment Schedule"
            status={getBlockStatus(
              touched?.paymentSchedule,
              errors.paymentSchedule
            )}
          >
            <PaymentSchedule
              values={values.paymentSchedule}
              errors={errors.paymentSchedule}
              setFieldValue={setFieldValue}
              validateField={validateField}
              rent={
                (lease ? parseStringPrice(lease?.rent) : rentInfo?.rent) ?? 0
              }
              financialInfo={values.financialInfo}
              dates={values.leaseInfo.dates}
              lease={lease}
            />
          </FormBlock>
        </div>
      </div>
      <div className="w-full flex justify-end px-4 pb-4">
        <Button
          className="text-xs md:text-base w-40"
          isLoading={progressStatus === ProgressStatus.LOADING}
          disabled={
            progressStatus === ProgressStatus.LOADING ||
            (lease && lease.status !== 'Draft')
          }
          isSubmit
        >
          Save
        </Button>
      </div>
    </Form>
  );
};

export default LeaseDetailsFormWrapper;
