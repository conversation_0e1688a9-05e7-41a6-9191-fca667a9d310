'use client';

import {
  updateDisbursementInfo,
  updateSecurityDepositQbInfo,
} from '@/app/actions/lease';
import { revalidateTagByName } from '@/app/actions/revalidateTag';
import DatePicker from '@/clients/components/common/DatePicker';
import Input from '@/clients/ui/input';
import { SecurityDepositReturnInfo } from '@/types/lease';
import { parseDateString } from '@/utils/common';
import { format } from 'date-fns';
import throttle from 'lodash/throttle';
import { memo, useCallback, useState } from 'react';
import toast from 'react-hot-toast';

type Props = {
  qbInfo: SecurityDepositReturnInfo;
  dateName: string;
  refName: string;
  paymendId: string;
  leaseId: number;
};

const SDFormItem = ({
  qbInfo,
  dateName,
  refName,
  paymendId,
  leaseId,
}: Props) => {
  const [dt, setDt] = useState<Date | undefined>(
    qbInfo[dateName] ? parseDateString(qbInfo[dateName]) : undefined
  );
  const [ref, setRef] = useState<string>(qbInfo[refName] ?? '');

  const onChangeDate = useCallback(
    (dt?: Date) => {
      if (dt) {
        setDt(dt);
        updateSecurityDepositQbInfo({
          security_deposit: paymendId,
          [dateName]: format(dt, 'yyyy-MM-dd'),
        })
          .then((data) => {
            console.log('data is', data);
            toast.success('Updated date.');
            revalidateTagByName(`lease-details-${leaseId}`);
          })
          .catch((e) => console.log({ e }));
      }
    },
    [dateName, leaseId, paymendId]
  );

  const onChangeText = throttle(
    useCallback((e: any) => {
      setRef(e.target.value);
    }, []),
    400
  );

  const onBlurTextInput = useCallback(() => {
    updateSecurityDepositQbInfo({
      security_deposit: paymendId,
      [refName]: ref,
    })
      .then((data) => {
        console.log('data is', data);
        revalidateTagByName(`lease-details-${leaseId}`);
        toast.success('Updated referrence.');
      })
      .catch((e) => console.log({ e }));
  }, [paymendId, ref, refName, leaseId]);

  return (
    <>
      <td className="py-2 text-center font-normal w-[18%]">
        <DatePicker
          className="!p-2 rounded-md"
          popOverClassName="p-0 w-full"
          selected={dt}
          onSelect={onChangeDate}
        />
      </td>
      <td className="py-2 text-center font-normal w-[26%]">
        <Input
          placeholder="Ref"
          value={ref}
          onChange={onChangeText}
          onBlur={onBlurTextInput}
          className="w-full !p-2 !text-xs"
        />
      </td>
    </>
  );
};

export default memo(SDFormItem);
